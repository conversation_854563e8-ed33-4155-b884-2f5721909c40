// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "UserCore",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "UserCore",
      targets: ["UserCore"]
    ),
    .library(
      name: "UserCoreTestsUtils",
      targets: ["UserCoreTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../../OGKit/Packages/OGUserCore")
  ],
  targets: [
    .target(
      name: "UserCore",
      dependencies: [
        "OGUserCore"
      ]
    ),
    .target(
      name: "UserCoreTestsUtils",
      dependencies: [
        "UserCore",
        .product(name: "OGUserCoreTestsUtils", package: "OGUserCore")
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "UserCoreTests",
      dependencies: [
        "UserCoreTestsUtils"
      ]
    )
  ]
)
