// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "Salutation",
  platforms: [.iOS(.v15)],
  products: [
    .library(
      name: "Salutation",
      targets: ["Salutation"]
    ),
    .library(
      name: "SalutationTestsUtils",
      targets: ["SalutationTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../AppCore"),
    .package(path: "../Account"),
    .package(path: "../../OGKit/Packages/OGSalutation"),
    .package(path: "../../OGKit/Packages/OGUserCore")
  ],
  targets: [
    .target(
      name: "Salutation",
      dependencies: [
        "Account",
        "AppCore",
        "OGSalutation",
        "OGUserCore"
      ]
    ),
    .target(
      name: "SalutationTestsUtils",
      dependencies: [
        .product(name: "AppCoreTestsUtils", package: "AppCore"),
        "Salutation"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "SalutationTests",
      dependencies: ["SalutationTestsUtils"],
      path: "Tests"
    )
  ]
)
