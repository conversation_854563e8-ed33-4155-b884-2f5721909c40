// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "Search",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "Search",
      targets: ["Search"]
    )
  ],
  dependencies: [
    .package(path: "../Account"),
    .package(path: "../AppCore"),
    .package(path: "../CatalogScanner"),
    .package(path: "../../OGKit/Packages/OGSearch"),
    .package(path: "../OGAsyncImage"),
    .package(path: "../UICatalog")
  ],
  targets: [
    .target(
      name: "Search",
      dependencies: [
        .product(name: "Account", package: "Account"),
        "AppCore",
        "CatalogScanner",
        "OGSearch",
        "OGAsyncImage",
        "UICatalog"
      ]
    ),
    .target(
      name: "SearchTestsUtils",
      dependencies: [
        "Search",
        .product(name: "Account", package: "Account"),
        .product(name: "AccountTestsUtils", package: "Account"),
        .product(name: "AppCoreTestsUtils", package: "AppCore")
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "SearchTests",
      dependencies: ["SearchTestsUtils"]
    )
  ]
)
