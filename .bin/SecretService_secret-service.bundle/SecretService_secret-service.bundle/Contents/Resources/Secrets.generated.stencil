// Generated using SecretService
// swiftlint:disable all
import Foundation
#if canImport(OGSecret)
import OGSecret
import OGCore
#endif
public enum Secrets {

  #if canImport(OGSecret)
  public static var ogSecrets: [OGSecret] {
    get {
      [{% for name in encodedPasswords %}
        {% if forloop.last %} OGSecret(identifier: #identifier("{{ name }}"), secret: Self.{{name}}) {% else %} OGSecret(identifier: #identifier("{{ name }}"), secret: Self.{{name}}), {% endif %} {% endfor %}
      ]
    }
  }
  #endif
  
  private static let salt: [UInt8] = {{ cipher }}
  {% for name in encodedPasswords %}
	public static var {{ name }}: String {
    let encoded: [UInt8] = {{encodedPasswords[name]}}
    return decode(encoded, cipher: salt)
  }
  {% endfor %}
	private static func decode(_ encoded: [UInt8], cipher: [UInt8]) -> String {
    let length = cipher.count
		var decrypted = [UInt8]()
		for k in encoded.enumerated() {
			decrypted.append(k.element ^ cipher[k.offset % length])
		}
		return String(bytes: decrypted, encoding: .utf8)!
  }
}
