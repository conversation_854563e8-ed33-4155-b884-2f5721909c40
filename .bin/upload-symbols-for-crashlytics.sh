#!/usr/bin/env bash
if [ "${PLATFORM_NAME}" != "iphonesimulator" ]; then
  "${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/run"
  EXITCODE=$?
  if [ $EXITCODE -eq 0 ]; then   
    echo "🎉 Uploaded symbols to Crashlytics."
  else
    echo "❌ Failed to upload symbols to Crashlytics, upload command result: $EXITCODE." 
  fi
  exit $EXITCODE

else
  echo "🧰 Info: Crashlytics upload only for non-simulator builds."   
fi