name: Deploy iOS betas to Firebase And Saucelabs (Wednesday and Sunday)

on:
  schedule:
      - cron: '0 18 * * 3,6' # 18:00 UTC on Wednesdays and Saturdays
  workflow_dispatch:
    inputs:
      trigger:
        description: 'dispatch trigger' # enables manual trigger per branches via actions gui, sample input: 'nightly'
        required: false
jobs:
  trigger-main:
    runs-on: macos-15

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.1
          bundler-cache: true
      
      - name: Clean DerivedData directory
        run: | 
          rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/*

      - name: Get all targets
        id: get-targets
        run: |
          TARGETS=($(./bin/all-app-targets.rb))
          echo "targets=${TARGETS[@]}" >> $GITHUB_ENV

      - name: Trigger main.yml workflow for each target
        uses: actions/github-script@v7
        with:
          script: |
            const { owner, repo } = context.repo;
            const workflow_id = 'main.yml';
            const ref = 'main';
            console.log(`TARGETS: ${process.env.targets}`); 
            const targets = process.env.targets.split(' ');

            for (const target of targets) {
              console.log(`Processing target: ${target}`);
              const inputs = {
                target: target,
                config: 'Beta',
                deployment_destination: 'FirebaseAndSaucelabs'
              };
              
              await github.rest.actions.createWorkflowDispatch({
                owner,
                repo,
                workflow_id,
                ref,
                inputs
              }).catch(error => error).then(response => {
                core.debug(response);
                if (response.status !== 204) {
                  core.setFailed(`create workflow_dispatch received status code ${response.status}`);
                }
              });

              console.log(`Triggered workflow for target: ${target}`);
            }
              
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}