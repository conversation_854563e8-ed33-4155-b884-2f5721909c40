name: Cleanup all actions caches
on:
  workflow_dispatch:
    inputs:
      trigger:
        description: 'dispatch trigger' # enables manual trigger per branches via actions gui, sample input: 'nightly'
        required: false
        
jobs:
  clear-actions-cache:
    name: Cleanup actions caches    
    runs-on: macos-14 
    steps:
    - name: Clear cache
      uses: actions/github-script@v7
      with:
        script: |
          console.log("About to clear")
          const caches = await github.rest.actions.getActionsCacheList({
            owner: context.repo.owner,
            repo: context.repo.repo,
          })
          for (const cache of caches.data.actions_caches) {
            console.log(cache)
            github.rest.actions.deleteActionsCacheById({
              owner: context.repo.owner,
              repo: context.repo.repo,
              cache_id: cache.id,
            })
          }
          console.log("Clear completed")

    env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}