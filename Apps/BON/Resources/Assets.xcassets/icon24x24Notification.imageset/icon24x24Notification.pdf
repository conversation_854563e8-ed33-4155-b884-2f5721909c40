%PDF-1.7

1 0 obj
  << /Length 2 0 R >>
stream
0.528000 0 0.070000 0.000000 0.474000 0.705000 d1

endstream
endobj

2 0 obj
  50
endobj

3 0 obj
  [ 0.528000 ]
endobj

4 0 obj
  << /Length 5 0 R >>
stream
/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo
<< /Registry (FigmaPDF)
   /Ordering (FigmaPDF)
   /Supplement 0
>> def
/CMapName /A-B-C def
/CMapType 2 def
1 begincodespacerange
<00> <FF>
endcodespacerange
1 beginbfchar
<00> <0031>
endbfchar
endcmap
CMapName currentdict /CMap defineresource pop
end
end
endstream
endobj

5 0 obj
  332
endobj

6 0 obj
  << /Subtype /Type3
     /CharProcs << /C0 1 0 R >>
     /Encoding << /Type /Encoding
                  /Differences [ 0 /C0 ]
               >>
     /Widths 3 0 R
     /FontBBox [ 0.000000 0.000000 0.000000 0.000000 ]
     /FontMatrix [ 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 ]
     /Type /Font
     /ToUnicode 4 0 R
     /FirstChar 0
     /LastChar 0
     /Resources << >>
  >>
endobj

7 0 obj
  << /Type /XObject
     /Length 8 0 R
     /Group << /Type /Group
               /S /Transparency
            >>
     /Subtype /Form
     /Resources << /Font << /F1 6 0 R >> >>
     /BBox [ 0.000000 0.000000 24.000000 24.000000 ]
  >>
stream
/DeviceRGB CS
/DeviceRGB cs
q
1.000000 0.000000 -0.000000 1.000000 2.500000 3.000000 cm
0.000000 0.000000 0.000000 scn
0.000000 9.000000 m
0.000000 13.970562 4.029438 18.000000 9.000000 18.000000 c
10.000000 18.000000 l
14.970563 18.000000 19.000000 13.970562 19.000000 9.000000 c
19.000000 9.000000 l
19.000000 4.029437 14.970562 0.000000 10.000000 0.000000 c
9.000000 0.000000 l
4.029437 0.000000 0.000000 4.029437 0.000000 9.000000 c
0.000000 9.000000 l
h
f
n
Q
q
1.000000 0.000000 -0.000000 1.000000 8.500000 14.380005 cm
1.000000 1.000000 1.000000 scn
0.330078 -6.380005 m
h
1.170078 -6.380005 m
1.170078 -4.952004 l
2.826078 -4.952004 l
2.826078 -0.548004 l
1.398078 -0.548004 l
1.398078 0.543995 l
1.814078 0.623995 2.166078 0.719995 2.454078 0.831995 c
2.742078 0.943995 3.022078 1.079996 3.294078 1.239996 c
4.590078 1.239996 l
4.590078 -4.952004 l
6.018079 -4.952004 l
6.018079 -6.380005 l
1.170078 -6.380005 l
h
f
n
Q
q
1.000000 0.000000 -0.000000 1.000000 8.500000 14.380005 cm
BT
12.000000 0.000000 0.000000 12.000000 0.330078 -6.380005 Tm
/F1 1.000000 Tf
[ (\000) ] TJ
ET
Q

endstream
endobj

8 0 obj
  1088
endobj

9 0 obj
  << /Type /XObject
     /Length 10 0 R
     /Group << /Type /Group
               /S /Transparency
            >>
     /Subtype /Form
     /Resources << >>
     /BBox [ 0.000000 0.000000 24.000000 24.000000 ]
  >>
stream
/DeviceRGB CS
/DeviceRGB cs
q
1.000000 0.000000 -0.000000 1.000000 2.500000 3.000000 cm
0.000000 0.000000 0.000000 scn
0.000000 9.000000 m
0.000000 13.970562 4.029438 18.000000 9.000000 18.000000 c
10.000000 18.000000 l
14.970563 18.000000 19.000000 13.970562 19.000000 9.000000 c
19.000000 9.000000 l
19.000000 4.029437 14.970562 0.000000 10.000000 0.000000 c
9.000000 0.000000 l
4.029437 0.000000 0.000000 4.029437 0.000000 9.000000 c
0.000000 9.000000 l
h
f
n
Q

endstream
endobj

10 0 obj
  465
endobj

11 0 obj
  << /XObject << /X1 7 0 R >>
     /ExtGState << /E1 << /SMask << /Type /Mask
                                    /G 9 0 R
                                    /S /Alpha
                                 >>
                          /Type /ExtGState
                       >> >>
  >>
endobj

12 0 obj
  << /Length 13 0 R >>
stream
/DeviceRGB CS
/DeviceRGB cs
q
/E1 gs
/X1 Do
Q

endstream
endobj

13 0 obj
  46
endobj

14 0 obj
  << /Annots []
     /Type /Page
     /MediaBox [ 0.000000 0.000000 24.000000 24.000000 ]
     /Resources 11 0 R
     /Contents 12 0 R
     /Parent 15 0 R
  >>
endobj

15 0 obj
  << /Kids [ 14 0 R ]
     /Count 1
     /Type /Pages
  >>
endobj

16 0 obj
  << /Pages 15 0 R
     /Type /Catalog
  >>
endobj

xref
0 17
0000000000 65535 f
0000000010 00000 n
0000000116 00000 n
0000000137 00000 n
0000000168 00000 n
0000000556 00000 n
0000000578 00000 n
0000000990 00000 n
0000002348 00000 n
0000002371 00000 n
0000003085 00000 n
0000003108 00000 n
0000003407 00000 n
0000003511 00000 n
0000003533 00000 n
0000003710 00000 n
0000003786 00000 n
trailer
<< /ID [ (some) (id) ]
   /Root 16 0 R
   /Size 17
>>
startxref
3847
%%EOF