{"features": [{"custom": {"domains": ["bonprixronew.media4u.pl"]}, "identifier": "allowedDomains"}, {"custom": {"api": "https://bonprixronew.media4u.pl/mobile-api/", "web": "https://bonprixronew.media4u.pl/"}, "identifier": "baseUrl"}, {"custom": {"host": "bonprixronew.media4u.pl", "password": "sF2hG6jd8e", "realm": "PASSWORD", "user": "clientuser"}, "identifier": "basicAuth", "isEnabled": true}, {"custom": {"supportedUrls": ["https://secure.payu.com", "https://www.computop-paygate.com", "https://www.payu.pl", "https://bonprixronew.media4u.pl/checkout/"]}, "identifier": "inAppBrowser"}, {"custom": {"toShop": "https://bonprixronew.media4u.pl/"}, "identifier": "onboarding"}, {"custom": {}, "identifier": "pushPromotion"}, {"custom": {"url": "https://app.adjust.com/hy3l6r_30m3rr"}, "identifier": "recommendation"}, {"custom": {"apiUrl": "https://bon-master.aac.ninja/api/", "secretIdentifier": "backendApiKeyBeta", "supportedUrls": ["app://deals"]}, "identifier": "deals"}]}