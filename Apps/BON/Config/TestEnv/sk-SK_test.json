{"features": [{"custom": {"domains": ["newtestsk.media4u.pl"]}, "identifier": "allowedDomains"}, {"custom": {"api": "https://newtestsk.bonprix.pl/mobile-api/", "web": "https://newtestsk.bonprix.pl/"}, "identifier": "baseUrl"}, {"custom": {"host": "newtestsk.bonprix.pl", "password": "ath1tohqu8AGeiV9eeru8u", "realm": "PASSWORD", "user": "newbonprixtestsk"}, "identifier": "basicAuth", "isEnabled": true}, {"custom": {"toShop": "https://newtestsk.media4u.pl/"}, "identifier": "onboarding"}, {"custom": {}, "identifier": "pushPromotion"}, {"custom": {"url": "https://app.adjust.com/hy3l6r_30m3rr"}, "identifier": "recommendation"}, {"custom": {"apiUrl": "https://bon-master.aac.ninja/api/", "secretIdentifier": "backendApiKeyBeta", "supportedUrls": ["app://deals"]}, "identifier": "deals"}]}