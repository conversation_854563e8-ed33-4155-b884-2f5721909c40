{"features": [{"custom": {"domains": ["newtesthu.media4u.pl"]}, "identifier": "allowedDomains"}, {"custom": {"api": "https://newtesthu.bonprix.pl/mobile-api/", "web": "https://newtesthu.bonprix.pl/"}, "identifier": "baseUrl"}, {"custom": {"host": "newtesthu.bonprix.pl", "password": "vaiphaifi8diehuijoo4Er", "realm": "PASSWORD", "user": "newbonprixtesthu"}, "identifier": "basicAuth", "isEnabled": true}, {"custom": {"toShop": "https://newtesthu.media4u.pl/"}, "identifier": "onboarding"}, {"custom": {"apiUrl": "https://bon-master.aac.ninja/api/", "secretIdentifier": "backendApiKeyBeta", "supportedUrls": ["app://deals"]}, "identifier": "deals"}]}