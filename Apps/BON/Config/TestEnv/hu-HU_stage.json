{"features": [{"custom": {"domains": ["bonprixhunew.media4u.pl"]}, "identifier": "allowedDomains"}, {"custom": {"api": "https://bonprixhunew.media4u.pl/mobile-api/", "web": "https://bonprixhunew.media4u.pl/"}, "identifier": "baseUrl"}, {"custom": {"host": "bonprixhunew.media4u.pl", "password": "sF2hG6jd8e", "realm": "PASSWORD", "user": "clientuser"}, "identifier": "basicAuth", "isEnabled": true}, {"custom": {"toShop": "https://bonprixhunew.media4u.pl/"}, "identifier": "onboarding"}, {"custom": {"apiUrl": "https://bon-master.aac.ninja/api/", "secretIdentifier": "backendApiKeyBeta", "supportedUrls": ["app://deals"]}, "identifier": "deals"}]}