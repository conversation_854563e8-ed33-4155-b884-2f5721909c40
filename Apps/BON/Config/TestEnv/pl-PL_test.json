{"features": [{"custom": {"supportedUrls": ["/my-account", "/myaccount", "app://openAccount", "app://openAccount/service"]}, "identifier": "account"}, {"custom": {"domains": ["newtestpl.media4u.pl"]}, "identifier": "allowedDomains"}, {"custom": {"api": "https://newtestpl.bonprix.pl/mobile-api/", "web": "https://newtestpl.bonprix.pl/"}, "identifier": "baseUrl"}, {"custom": {"host": "newtestpl.bonprix.pl", "password": "d2Sj5Fk2Ej3Lf5Cd6PsD2mU4", "realm": "PASSWORD", "user": "newbonprixtestpl"}, "identifier": "basicAuth", "isEnabled": true}, {"custom": {"toShop": "https://newtestpl.media4u.pl/"}, "identifier": "onboarding"}, {"custom": {}, "identifier": "pushPromotion"}, {"custom": {"url": "https://app.adjust.com/hy3l6r_30m3rr"}, "identifier": "recommendation"}, {"custom": {"apiUrl": "https://bon-master.aac.ninja/api/", "secretIdentifier": "backendApiKeyBeta", "supportedUrls": ["app://deals"]}, "identifier": "deals"}]}