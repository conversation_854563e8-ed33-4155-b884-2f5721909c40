// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import OGAirshipKit

public struct AirshipTrackingFeatureConfiguration: AirshipTrackingFeatureConfigurable {
  public var isEnabled: Bool = true
  
  public var attributeIdMapping: [String: String] = [:]
  public var eventIdMapping: [String: Bool] = [
    "basket_badge_count": true,
    "user_info": true,
    "purchase_completed": true
  ]
  public var keepContactAssociation: Bool = true
}

extension OGAirshipTrackingFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    airshipTracking.register {
      OGAirshipTrackingFeatureAdapter(configuration: AirshipTrackingFeatureConfiguration())
    }
  }
}
