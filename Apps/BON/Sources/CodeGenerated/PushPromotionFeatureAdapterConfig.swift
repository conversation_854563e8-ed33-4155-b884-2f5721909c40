// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import PushPromotion
public struct PushPromotionFeatureConfiguration: PushPromotionFeatureConfigurable {
	public var isEnabled: Bool = true
  
	public var supportedUrls: [URL] = [URL(string:"app://pushPromotionLayer")!] 
}

extension PushPromotionFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		pushPromotion.register {
			PushPromotionFeatureAdapter(configuration: PushPromotionFeatureConfiguration())
		}
	}
}

