// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import AppUpdate
public struct AppUpdateFeatureConfiguration: AppUpdateFeatureConfigurable {
  public var isEnabled: Bool = false
  
  public var actionText: String? = nil
  public var bodyText: String? = nil
  public var headlineText: String? = nil
	public var externalTargetUrl: String = "itms-apps://itunes.apple.com/app/id1317009356"
	public var isAllowedToStart: Bool = true
}

extension AppUpdateFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		appUpdate.register {
			AppUpdateFeatureAdapter(configuration: AppUpdateFeatureConfiguration())
		}
	}
}

