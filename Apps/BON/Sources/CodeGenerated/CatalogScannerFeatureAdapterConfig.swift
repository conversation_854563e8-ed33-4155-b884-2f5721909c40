// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import CatalogScanner
public struct CatalogScannerFeatureConfiguration: CatalogScannerFeatureConfigurable {
	public var isEnabled: Bool = false
	
	public var catalogEndpoint: String = "" 
	public var catalogOrderUrl: String = "" 
	public var secretIdentifier: String = "" 
	public var supportedUrls: [URL] = [URL(string:"app://openCatalogScanner")!] 
}

extension CatalogScannerFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		catalogScanner.register {
			CatalogScannerFeatureAdapter(configuration: CatalogScannerFeatureConfiguration())
		}
	}
}

