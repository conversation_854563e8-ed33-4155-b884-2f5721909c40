// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import Search

public struct SearchFeatureConfiguration: SearchFeatureConfigurable {
  
	public var isEnabled: Bool = true
	
	public var debounceMillis: Int = 500 
	public var maxHistoryItems: Int = 5 
	public var minCharacters: Int = 2 
	public var secretIdentifier: String = "backendApiKey" 
	public var suggestionsApiUrl: String = "https:/bon.aac.ninja/api/search/suggestions?query=" 
	public var supportedUrls: [URL] = [URL(string:"app://search")!] 
	public var webPath: String = "/mobile-api/search/?term="
  public var shortcuts: [Shortcut] = []
}

extension SearchFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		search.register {
			SearchFeatureAdapter(configuration: SearchFeatureConfiguration())
		}
	}
}

