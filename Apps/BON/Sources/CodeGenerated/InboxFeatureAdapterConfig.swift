// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import OGAirshipKit
public struct InboxFeatureConfiguration: InboxFeatureConfigurable {
	public var isEnabled: Bool = true
	
	public var deleteMessagesAfterDays: Int = 30 
	public var deleteMessagesAfterTenantChange: Bool = true 
	public var forceMessageWebView: Bool = false
  public var shouldShowThumbnails: Bool = false
	public var supportedUrls: [URL] = [URL(string:"app://openInbox")!,URL(string:"app://inbox/overview")!]
}

extension InboxFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		inbox.register {
			InboxFeatureAdapter(configuration: InboxFeatureConfiguration())
		}
	}
}

