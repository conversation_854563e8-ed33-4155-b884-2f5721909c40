// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import PromoBanner
public struct PromoBannerFeatureConfiguration: PromoBannerFeatureConfigurable {
	public var isEnabled: Bool = false
	
	public var promoPattern: String = "" 
	public var webbridgeTrigger: Bool = false 
}

extension PromoBannerFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		promoBanner.register {
			PromoBannerFeatureAdapter(configuration: PromoBannerFeatureConfiguration())
		}
	}
}

