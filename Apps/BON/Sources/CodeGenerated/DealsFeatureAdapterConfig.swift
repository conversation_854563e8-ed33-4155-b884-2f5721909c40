// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import Deals
public struct DealsFeatureConfiguration: DealsFeatureConfigurable {
	public var isEnabled: Bool = false
	
	public var apiUrl: String = "https://bon.aac.ninja/api/" 
	public var secretIdentifier: String = "backendApiKey" 
	public var supportedUrls: [URL] = [URL(string:"app://deals")!] 
}

extension DealsFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		deals.register {
			DealsFeatureAdapter(configuration: DealsFeatureConfiguration())
		}
	}
}

