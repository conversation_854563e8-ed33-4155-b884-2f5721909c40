// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import Review
public struct ReviewFeatureConfiguration: ReviewFeatureConfigurable {
	public var isEnabled: Bool = true
	
	public var supportedUrls: [URL] = [URL(string:"app://review")!] 
}

extension ReviewFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		review.register {
			ReviewFeatureAdapter(configuration: ReviewFeatureConfiguration())
		}
	}
}

