// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import Login
public struct LoginFeatureConfiguration: LoginFeatureConfigurable {  
	public var isEnabled: Bool = true
  public var webPath: String = ""
  public var showTabBarBadge: Bool = false
	public var supportedUrls: [String] = ["^.*my-account/login$"]
}

extension LoginFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		login.register {
			LoginFeatureAdapter(configuration: LoginFeatureConfiguration())
		}
	}
}

