// Generated using OGFeatureAdapterMaker
// swiftlint:disable all

import Account
import Foundation
import OGDIService
import OGFeatureCore

public struct AccountFeatureConfiguration: AccountFeatureConfigurable {
  
  public var isEnabled: Bool = true
  
  public var accountUrl: String = "my-account"
  public var loginUrl: String = "my-account/login"
  public var logoutUrl: String = "my-account/logout"
  public var secretIdentifier: String = "navigationApiKey"
  public var serviceEndpoint: String = "mobile-api/navigation/service"
  public var showAsLoggedIn: Bool = true
  public var staticEntriesBottom: [AccountNavigationEntry] = []
  public var staticEntriesTop: [AccountNavigationEntry] = []
  public var supportedUrls: [URL] = [URL(string: "app://account")!]
  public var apiKey: String? = "bon-api-key"
}

extension AccountFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		account.register {
			AccountFeatureAdapter(configuration: AccountFeatureConfiguration())
		}
	}
}

