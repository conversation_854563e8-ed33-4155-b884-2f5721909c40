// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import PushPromotion
public struct PushPromotionBannerFeatureConfiguration: PushPromotionBannerFeatureConfigurable {
  public var isEnabled: Bool = false
   
  public var showAfterDays: Int = 0
}

extension PushPromotionBannerFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    pushPromotionBanner.register {
      PushPromotionBannerFeatureAdapter(configuration: PushPromotionBannerFeatureConfiguration())
    }
  }
}

