// swiftlint:disable all
import Foundation
import OGCopyCodeBanner
import OGDIService
import OGFeatureCore

public struct CopyCodeBannerFeatureConfiguration: CopyCodeBannerFeatureConfigurable {
  public var isEnabled: Bool = false
  public var supportedUrls: [String] = []
}

extension CopyCodeBannerFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    copyCodeBanner.register {
      CopyCodeBannerFeatureAdapter(configuration: CopyCodeBannerFeatureConfiguration())
    }
  }
}

