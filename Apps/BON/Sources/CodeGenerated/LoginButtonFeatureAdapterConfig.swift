// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import OGLoginButton

public struct LoginButtonFeatureConfiguration: LoginButtonFeatureConfigurable {
  public var isEnabled: Bool = false

  public var navigationTitle: LoginButtonConfiguration = .init(
    enabledUrls: ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(hu|pl|ro|ru|sk)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"]
  )
}

extension LoginButtonFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		loginButton.register {
			LoginButtonFeatureAdapter(configuration: LoginButtonFeatureConfiguration())
		}
	}
}

