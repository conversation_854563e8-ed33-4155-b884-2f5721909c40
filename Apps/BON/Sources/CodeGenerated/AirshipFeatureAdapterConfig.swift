// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import OGAirshipKit

public struct AirshipFeatureConfiguration: AirshipFeatureConfigurable {
  public var cloudSite: String = "eu"
	public var isEnabled: Bool = true
	
	public var attributeIdMapping: [String: String] = [:]
	public var eventIdMapping: [String: Bool] = [
    "basket_badge_count": true,
    "user_info": true,
    "purchase_completed": true
  ] 
	public var keepContactAssociation: Bool = true
}

extension OGAirshipFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    airship.register {
      OGAirshipFeatureAdapter(configuration: AirshipFeatureConfiguration())
    }
  }
}
