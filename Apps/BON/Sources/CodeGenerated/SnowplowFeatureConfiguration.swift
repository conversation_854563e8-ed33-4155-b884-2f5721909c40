import Foundation
import OGDIService
import OGFeatureCore
import OGTracker

public struct SnowplowFeatureConfiguration: SnowplowFeatureConfigurable {
  
  public var appId: String = ""
  public var isEnabled: Bool = false
  public var namespace: String = ""
  public var pushPath: String = ""
  public var collectorUrl: String = ""
}

extension OGSnowplowFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    adapter.register {
      OGSnowplowFeatureAdapter(configuration: SnowplowFeatureConfiguration())
    }
  }
}
