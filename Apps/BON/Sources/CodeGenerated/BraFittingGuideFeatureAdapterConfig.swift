// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import BraFittingGuide
public struct BraFittingGuideFeatureConfiguration: BraFittingGuideFeatureConfigurable {
	public var isEnabled: Bool = false
	
	public var configEndpoint: String = "" 
	public var joinRecommendationWithInfoString: String = ""  
	public var measureUrl: String = ""  
	public var recommendationEndpoint: String = ""
  public var secretIdentifier: String = ""
	public var supportedUrls: [URL] = [URL(string:"app://openBraFittingGuide")!]
}

extension BraFittingGuideFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		braFittingGuide.register {
			BraFittingGuideFeatureAdapter(configuration: BraFittingGuideFeatureConfiguration())
		}
	}
}

