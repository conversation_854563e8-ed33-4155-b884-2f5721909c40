// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import OGWebView
import OGSalutation

public struct SalutationFeatureConfiguration: SalutationFeatureConfigurable {
  
  public var isEnabled: Bool = false
  public var salutationUrls: [String] = []
}

extension SalutationFeatureAdapterContainer: AutoRegistering {
  
  public func autoRegister() {
    salutation.register {
      SalutationFeatureAdapter(configuration: SalutationFeatureConfiguration())
    }
  }
}

