// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import Recommendation
public struct RecommendationFeatureConfiguration: RecommendationFeatureConfigurable {
	public var isEnabled: Bool = true
	
	public var message: String = "" 
	public var supportedUrls: [URL] = [] 
	public var url: String = "" 
}

extension RecommendationFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		recommendation.register {
			RecommendationFeatureAdapter(configuration: RecommendationFeatureConfiguration())
		}
	}
}

